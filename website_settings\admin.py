from django.contrib import admin
from django.utils.html import format_html
from .models import WebsiteSettings


@admin.register(WebsiteSettings)
class WebsiteSettingsAdmin(admin.ModelAdmin):
    """
    Admin interface for WebsiteSettings model.
    """

    def has_add_permission(self, _request):
        """
        Prevent adding multiple instances (singleton pattern).
        """
        return not WebsiteSettings.objects.exists()

    def has_delete_permission(self, _request, _obj=None):
        """
        Prevent deletion of the settings instance.
        """
        return False

    def changelist_view(self, request, extra_context=None):
        """
        Redirect to the single instance edit page if it exists.
        """
        if WebsiteSettings.objects.exists():
            settings = WebsiteSettings.objects.first()
            from django.shortcuts import redirect
            from django.urls import reverse
            return redirect(reverse('admin:website_settings_websitesettings_change', args=[settings.pk]))
        return super().changelist_view(request, extra_context)

    # Field organization
    fieldsets = (
        ('Basic Information', {
            'fields': ('website_name', 'tagline', 'logo', 'logo_preview')
        }),
        ('Contact Information', {
            'fields': ('contact_email', 'contact_phone', 'contact_address')
        }),
        ('Social Media Links', {
            'fields': ('facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url', 'youtube_url'),
            'classes': ('collapse',)
        }),
        ('Legal & Copyright', {
            'fields': ('copyright_text', 'privacy_policy', 'terms_of_service', 'refund_policy'),
            'classes': ('collapse',)
        }),
        ('SEO Settings', {
            'fields': ('meta_title', 'meta_description', 'meta_keywords'),
            'classes': ('collapse',)
        }),
        ('Maintenance Mode', {
            'fields': ('maintenance_mode', 'maintenance_message'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

    readonly_fields = ('logo_preview', 'created_at', 'updated_at')

    list_display = ('website_name', 'contact_email', 'maintenance_mode', 'updated_at')

    def logo_preview(self, obj):
        """
        Display logo preview in admin.
        """
        if obj.logo:
            return format_html(
                '<img src="{}" style="max-height: 100px; max-width: 200px;" />',
                obj.logo.url
            )
        return "No logo uploaded"
    logo_preview.short_description = "Logo Preview"

    class Media:
        css = {
            'all': ('admin/css/website_settings.css',)
        }
        js = ('admin/js/website_settings.js',)
