"""
Database router for microservices architecture.
Routes models to their respective databases.
"""

class DatabaseRouter:
    """
    A router to control all database operations on models for different
    microservices to ensure they use their dedicated databases.
    """

    # Define which apps use which databases
    route_app_labels = {
        'authentication': 'default',  # Uses default database
        'catalogue': 'catalogue_db',
        'cart': 'cart_db',
        'coupons': 'coupons_db',
        'orders': 'orders_db',
        'payments': 'payments_db',
        'providers': 'providers_db',
        'taxation': 'default',  # Tax configuration in default database for global access
        'scheduling': 'scheduling_db',  # Scheduling and slot management
        'website_settings': 'default',  # Website settings in default database for global access
    }

    def db_for_read(self, model, **hints):
        """Suggest the database to read from."""
        if model._meta.app_label in self.route_app_labels:
            return self.route_app_labels[model._meta.app_label]
        # Core Django apps and any other unrouted apps go to default
        return 'default'

    def db_for_write(self, model, **hints):
        """Suggest the database to write to."""
        if model._meta.app_label in self.route_app_labels:
            return self.route_app_labels[model._meta.app_label]
        # Core Django apps and any other unrouted apps go to default
        return 'default'

    def allow_relation(self, obj1, obj2, **hints):
        """
        Allow relations if both objects are in the same database.
        Crucially, allow relations between custom apps and core Django apps in the 'default' database.
        """
        db_obj1 = self.route_app_labels.get(obj1._meta.app_label, 'default')
        db_obj2 = self.route_app_labels.get(obj2._meta.app_label, 'default')

        # If both objects are in the same database, allow the relation
        if db_obj1 == db_obj2:
            return True

        # Allow relations between explicitly routed custom apps and core Django apps in the 'default' database
        # This is CRUCIAL for Foreign Keys to User, ContentType, etc.
        routed_app_dbs = set(self.route_app_labels.values())
        default_db_core_apps = {'auth', 'contenttypes', 'authentication'}  # Core apps in default DB

        # Scenario 1: A custom app model refers to a core Django model in 'default'
        if db_obj1 in routed_app_dbs and db_obj2 == 'default' and obj2._meta.app_label in default_db_core_apps:
            return True

        # Scenario 2: A core Django model in 'default' refers to a custom app model
        # (less common, but included for completeness)
        if db_obj2 in routed_app_dbs and db_obj1 == 'default' and obj1._meta.app_label in default_db_core_apps:
            return True

        return False  # Disallow all other relations

    def allow_migrate(self, db, app_label, model_name=None, **hints):
        """
        Make sure core Django apps only appear in the 'default' database.
        Custom apps are routed to their specific database.
        """
        # If the app is one of our explicitly routed custom apps
        if app_label in self.route_app_labels:
            return db == self.route_app_labels[app_label]

        # If the app is one of Django's core apps (auth, contenttypes, admin, sessions)
        # or any other app not explicitly routed, it should only migrate to the 'default' database.
        elif db == 'default':
            return True

        # Don't allow any other apps to migrate to non-default databases unless specifically routed
        return False
