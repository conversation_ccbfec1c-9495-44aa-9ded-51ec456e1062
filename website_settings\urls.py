from django.urls import path
from . import views

app_name = 'website_settings'

urlpatterns = [
    # Public API endpoint (no authentication required)
    path('site-info/', views.public_site_info, name='public-site-info'),
    
    # Admin API endpoints (authentication required)
    path('admin/settings/', views.admin_site_settings, name='admin-site-settings'),
    path('admin/settings/update/', views.update_site_settings, name='update-site-settings'),
    
    # Alternative endpoint using class-based view
    path('admin/settings/detail/', views.WebsiteSettingsDetailView.as_view(), name='settings-detail'),
]
