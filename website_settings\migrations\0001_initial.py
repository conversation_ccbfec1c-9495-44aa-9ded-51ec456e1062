# Generated by Django 4.2.7 on 2025-06-28 06:54

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='WebsiteSettings',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('website_name', models.CharField(default='Home Services', help_text='Name of the website/business', max_length=255)),
                ('tagline', models.CharField(blank=True, help_text='Website tagline or slogan', max_length=500, null=True)),
                ('logo', models.ImageField(blank=True, help_text='Website logo image', null=True, upload_to='website/logos/')),
                ('contact_email', models.EmailField(blank=True, help_text='Primary contact email address', max_length=254, null=True, validators=[django.core.validators.EmailValidator()])),
                ('contact_phone', models.CharField(blank=True, help_text='Primary contact phone number', max_length=20, null=True)),
                ('contact_address', models.TextField(blank=True, help_text='Business address', null=True)),
                ('facebook_url', models.URLField(blank=True, help_text='Facebook page URL', max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('instagram_url', models.URLField(blank=True, help_text='Instagram profile URL', max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('twitter_url', models.URLField(blank=True, help_text='Twitter profile URL', max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('linkedin_url', models.URLField(blank=True, help_text='LinkedIn profile URL', max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('youtube_url', models.URLField(blank=True, help_text='YouTube channel URL', max_length=500, null=True, validators=[django.core.validators.URLValidator()])),
                ('copyright_text', models.CharField(default='© 2024 Home Services. All rights reserved.', help_text='Copyright text displayed in footer', max_length=255)),
                ('privacy_policy', models.TextField(blank=True, help_text='Privacy policy content (supports HTML)', null=True)),
                ('terms_of_service', models.TextField(blank=True, help_text='Terms of service content (supports HTML)', null=True)),
                ('refund_policy', models.TextField(blank=True, help_text='Refund policy content (supports HTML)', null=True)),
                ('maintenance_mode', models.BooleanField(default=False, help_text='Enable maintenance mode to show maintenance page')),
                ('maintenance_message', models.TextField(blank=True, help_text='Message to display during maintenance mode', null=True)),
                ('meta_title', models.CharField(blank=True, help_text='Default meta title for SEO', max_length=255, null=True)),
                ('meta_description', models.TextField(blank=True, help_text='Default meta description for SEO', null=True)),
                ('meta_keywords', models.CharField(blank=True, help_text='Default meta keywords for SEO (comma-separated)', max_length=500, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Website Settings',
                'verbose_name_plural': 'Website Settings',
            },
        ),
    ]
