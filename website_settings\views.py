from rest_framework import generics, permissions, status
from rest_framework.decorators import api_view, permission_classes
from rest_framework.response import Response
from django.http import Http404
from .models import WebsiteSettings
from .serializers import WebsiteSettingsSerializer, PublicWebsiteSettingsSerializer


class WebsiteSettingsDetailView(generics.RetrieveUpdateAPIView):
    """
    Retrieve and update website settings (Admin only).
    """
    serializer_class = WebsiteSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]

    def get_object(self):
        """
        Get the single website settings instance.
        """
        try:
            return WebsiteSettings.get_settings()
        except WebsiteSettings.DoesNotExist:
            raise Http404("Website settings not found")

    def get_permissions(self):
        """
        Only staff users can update settings.
        """
        if self.request.method in ['PUT', 'PATCH']:
            return [permissions.IsAuthenticated(), permissions.IsAdminUser()]
        return [permissions.IsAuthenticated()]


@api_view(['GET'])
@permission_classes([permissions.AllowAny])
def public_site_info(request):
    """
    Public API endpoint to get website settings for frontend consumption.
    No authentication required.
    """
    try:
        settings = WebsiteSettings.get_settings()
        serializer = PublicWebsiteSettingsSerializer(settings)

        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Failed to retrieve website settings',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def admin_site_settings(request):
    """
    Admin API endpoint to get complete website settings.
    Requires authentication.
    """
    try:
        # Check if user is staff
        if not request.user.is_staff and request.user.user_type != 'STAFF':
            return Response({
                'success': False,
                'message': 'Permission denied. Staff access required.'
            }, status=status.HTTP_403_FORBIDDEN)

        settings = WebsiteSettings.get_settings()
        serializer = WebsiteSettingsSerializer(settings)

        return Response({
            'success': True,
            'data': serializer.data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Failed to retrieve website settings',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['PUT', 'PATCH'])
@permission_classes([permissions.IsAuthenticated])
def update_site_settings(request):
    """
    Admin API endpoint to update website settings.
    Requires staff authentication.
    """
    try:
        # Check if user is staff
        if not request.user.is_staff and request.user.user_type != 'STAFF':
            return Response({
                'success': False,
                'message': 'Permission denied. Staff access required.'
            }, status=status.HTTP_403_FORBIDDEN)

        settings = WebsiteSettings.get_settings()
        serializer = WebsiteSettingsSerializer(
            settings,
            data=request.data,
            partial=(request.method == 'PATCH')
        )

        if serializer.is_valid():
            serializer.save()
            return Response({
                'success': True,
                'message': 'Website settings updated successfully',
                'data': serializer.data
            }, status=status.HTTP_200_OK)

        return Response({
            'success': False,
            'message': 'Validation failed',
            'errors': serializer.errors
        }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        return Response({
            'success': False,
            'message': 'Failed to update website settings',
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
