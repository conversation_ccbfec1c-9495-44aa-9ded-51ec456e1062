from rest_framework import serializers
from .models import WebsiteSettings


class WebsiteSettingsSerializer(serializers.ModelSerializer):
    """
    Serializer for WebsiteSettings model with all fields for admin use.
    """
    logo_url = serializers.SerializerMethodField()
    social_media_links = serializers.SerializerMethodField()
    
    class Meta:
        model = WebsiteSettings
        fields = [
            'id', 'website_name', 'tagline', 'logo', 'logo_url',
            'contact_email', 'contact_phone', 'contact_address',
            'facebook_url', 'instagram_url', 'twitter_url', 'linkedin_url', 'youtube_url',
            'social_media_links', 'copyright_text', 'privacy_policy', 'terms_of_service',
            'refund_policy', 'maintenance_mode', 'maintenance_message',
            'meta_title', 'meta_description', 'meta_keywords',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at', 'logo_url', 'social_media_links']
    
    def get_logo_url(self, obj):
        """Get the full URL for the logo image."""
        return obj.get_logo_url()
    
    def get_social_media_links(self, obj):
        """Get all social media links as a dictionary."""
        return obj.get_social_media_links()


class PublicWebsiteSettingsSerializer(serializers.ModelSerializer):
    """
    Public serializer for WebsiteSettings with limited fields for frontend consumption.
    """
    logo_url = serializers.SerializerMethodField()
    social_media_links = serializers.SerializerMethodField()
    
    class Meta:
        model = WebsiteSettings
        fields = [
            'website_name', 'tagline', 'logo_url', 'contact_email', 'contact_phone',
            'contact_address', 'social_media_links', 'copyright_text',
            'privacy_policy', 'terms_of_service', 'refund_policy',
            'meta_title', 'meta_description', 'meta_keywords'
        ]
    
    def get_logo_url(self, obj):
        """Get the full URL for the logo image."""
        return obj.get_logo_url()
    
    def get_social_media_links(self, obj):
        """Get all social media links as a dictionary."""
        return obj.get_social_media_links()
