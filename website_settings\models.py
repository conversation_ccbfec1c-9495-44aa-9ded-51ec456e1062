from django.db import models
from django.core.validators import URLValidator, EmailValidator
from django.core.exceptions import ValidationError


class WebsiteSettings(models.Model):
    """
    Global website settings and configuration.
    This model should have only one instance (singleton pattern).
    """

    # Basic Website Information
    website_name = models.CharField(
        max_length=255,
        default="Home Services",
        help_text="Name of the website/business"
    )

    tagline = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        help_text="Website tagline or slogan"
    )

    logo = models.ImageField(
        upload_to='website/logos/',
        blank=True,
        null=True,
        help_text="Website logo image"
    )

    # Contact Information
    contact_email = models.EmailField(
        blank=True,
        null=True,
        validators=[EmailValidator()],
        help_text="Primary contact email address"
    )

    contact_phone = models.CharField(
        max_length=20,
        blank=True,
        null=True,
        help_text="Primary contact phone number"
    )

    contact_address = models.TextField(
        blank=True,
        null=True,
        help_text="Business address"
    )

    # Social Media Links
    facebook_url = models.URLField(
        max_length=500,
        blank=True,
        null=True,
        validators=[URLValidator()],
        help_text="Facebook page URL"
    )

    instagram_url = models.URLField(
        max_length=500,
        blank=True,
        null=True,
        validators=[URLValidator()],
        help_text="Instagram profile URL"
    )

    twitter_url = models.URLField(
        max_length=500,
        blank=True,
        null=True,
        validators=[URLValidator()],
        help_text="Twitter profile URL"
    )

    linkedin_url = models.URLField(
        max_length=500,
        blank=True,
        null=True,
        validators=[URLValidator()],
        help_text="LinkedIn profile URL"
    )

    youtube_url = models.URLField(
        max_length=500,
        blank=True,
        null=True,
        validators=[URLValidator()],
        help_text="YouTube channel URL"
    )

    # Copyright and Legal
    copyright_text = models.CharField(
        max_length=255,
        default="© 2024 Home Services. All rights reserved.",
        help_text="Copyright text displayed in footer"
    )

    # Policy Content
    privacy_policy = models.TextField(
        blank=True,
        null=True,
        help_text="Privacy policy content (supports HTML)"
    )

    terms_of_service = models.TextField(
        blank=True,
        null=True,
        help_text="Terms of service content (supports HTML)"
    )

    refund_policy = models.TextField(
        blank=True,
        null=True,
        help_text="Refund policy content (supports HTML)"
    )

    # Additional Settings
    maintenance_mode = models.BooleanField(
        default=False,
        help_text="Enable maintenance mode to show maintenance page"
    )

    maintenance_message = models.TextField(
        blank=True,
        null=True,
        help_text="Message to display during maintenance mode"
    )

    # SEO Settings
    meta_title = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text="Default meta title for SEO"
    )

    meta_description = models.TextField(
        blank=True,
        null=True,
        help_text="Default meta description for SEO"
    )

    meta_keywords = models.CharField(
        max_length=500,
        blank=True,
        null=True,
        help_text="Default meta keywords for SEO (comma-separated)"
    )

    # Timestamps
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Website Settings"
        verbose_name_plural = "Website Settings"

    def save(self, *args, **kwargs):
        """
        Ensure only one instance exists (singleton pattern).
        """
        if not self.pk and WebsiteSettings.objects.exists():
            raise ValidationError("Only one WebsiteSettings instance is allowed.")
        super().save(*args, **kwargs)

    @classmethod
    def get_settings(cls):
        """
        Get the website settings instance, create if doesn't exist.
        """
        settings, _ = cls.objects.get_or_create(pk=1)
        return settings

    def get_logo_url(self):
        """
        Get the full URL for the logo image.
        """
        if self.logo:
            return self.logo.url
        return None

    def get_social_media_links(self):
        """
        Get all social media links as a dictionary.
        """
        return {
            'facebook': self.facebook_url,
            'instagram': self.instagram_url,
            'twitter': self.twitter_url,
            'linkedin': self.linkedin_url,
            'youtube': self.youtube_url,
        }

    def __str__(self):
        return f"Website Settings - {self.website_name}"
